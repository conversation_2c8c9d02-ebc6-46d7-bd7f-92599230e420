-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 16, 2025 at 09:42 AM
-- Table: autobooks_navigation
-- Records: 16

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autobooks_navigation`
--

DROP TABLE IF EXISTS `autobooks_navigation`;
CREATE TABLE `autobooks_navigation` (
  `id` int(10) unsigned NOT NULL DEFAULT 0,
  `parent_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'Parent path for hierarchical navigation',
  `route_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Unique route identifier within parent path',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display name for the navigation item',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Icon identifier for the navigation item',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '\\' COMMENT 'location to look for the view file, relative to view directory',
  `required_roles` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JSON array of roles required to access this route',
  `sort_order` int(11) DEFAULT 0 COMMENT 'Sort order for navigation items within the same parent',
  `show_navbar` tinyint(1) DEFAULT 1 COMMENT 'Whether to show this item in the navigation bar',
  `can_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Can be deleted in the UI, reserved for user added views',
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'If true file_path is relative to FS_SYS_VIEWS otherwise relative to FS_VIEWS',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Dumping data for table `autobooks_navigation`
--

INSERT INTO `autobooks_navigation` (`id`, `parent_path`, `route_key`, `name`, `icon`, `file_path`, `required_roles`, `sort_order`, `show_navbar`, `can_delete`, `is_system`, `created_at`, `updated_at`) VALUES
('2', 'root', 'dashboard', 'Dashboard', 'chart', 'dashboard', '[]', '1', '1', '', '1', '2025-05-18 23:10:38', '2025-08-08 15:25:05'),
('3', 'autodesk', 'orders', 'Orders', 'shopping-cart', 'autodesk', '[]', '3', '1', '', '', '2025-05-18 23:10:38', '2025-07-18 00:48:33'),
('4', 'autodesk', 'quotes', 'Quotes', 'speech_bubble', 'autodesk', '[]', '4', '1', '', '', '2025-05-18 23:10:38', '2025-07-12 13:41:33'),
('5', 'autodesk', 'subscriptions', 'Subscriptions', 'ticket', 'autodesk', '[]', '6', '1', '', '', '2025-05-18 23:10:38', '2025-07-12 13:41:33'),
('6', 'autodesk', 'customers', 'Customers', 'user', 'autodesk', '[]', '2', '1', '', '', '2025-05-18 23:10:38', '2025-07-18 00:48:26'),
('7', 'autodesk', 'products', 'Products', 'computer_desktop', 'autodesk', '[]', '5', '1', '', '', '2025-05-18 23:10:38', '2025-07-18 00:48:43'),
('8', 'root', 'system', 'System', 'code', 'system', '[\"dev\"]', '10', '', '', '1', '2025-05-18 23:10:38', '2025-08-11 15:27:08'),
('9', 'system', 'logs', 'logs', 'code', 'system', '[]', '3', '1', '', '1', '2025-05-18 23:10:38', '2025-08-07 20:11:37'),
('10', 'root', 'autodesk', 'Autodesk', 'autodesk', '', '[]', '2', '1', '', '', '2025-07-05 17:06:39', '2025-08-11 14:16:31'),
('35', 'root', 'email_campaigns', 'Email Campaigns', 'envelope', 'email_campaigns', '[\"admin\",\"dev\",\"manager\"]', '15', '1', '', '1', '2025-07-20 00:56:30', '2025-08-11 15:27:08'),
('38', 'system', 'data_sources', 'Data Sources', 'circle-stack', 'data_sources', '[]', '4', '1', '', '1', '2025-07-22 14:42:20', '2025-08-07 20:11:37'),
('52', 'system', 'subscription_matching_rules', 'Rule Manager', 'puzzle-piece', 'system', '[]', '14', '1', '1', '1', '2025-08-07 20:05:31', '2025-08-11 11:12:09'),
('57', 'system', 'users', 'users', 'user-group', '', '[\"admin\",\"dev\"]', '15', '1', '1', '1', '2025-08-08 15:23:36', '2025-08-11 13:04:38'),
('0', 'root', 'system', 'System', 'cog', '', '[\"admin\",\"dev\"]', '100', '1', '', '1', '2025-08-14 16:12:43', '2025-08-14 16:12:43'),
('0', 'system', 'database_dump', 'Database Dump Manager', 'database', 'system', '[\"admin\",\"dev\"]', '10', '1', '', '1', '2025-08-14 16:12:43', '2025-08-14 16:12:43'),
('0', 'root', 'bluebeam', 'Bluebeam', 'bluebeam', '', '[]', '101', '1', '1', '', '2025-08-16 09:46:00', '2025-08-16 09:46:00');

COMMIT;
