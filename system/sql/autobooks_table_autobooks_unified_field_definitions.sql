-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 16, 2025 at 09:42 AM
-- Table: autobooks_unified_field_definitions
-- Records: 2

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `autobooks_unified_field_definitions`
--

DROP TABLE IF EXISTS `autobooks_unified_field_definitions`;
CREATE TABLE `autobooks_unified_field_definitions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `field_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `field_definition` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `field_name` (`field_name`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `autobooks_unified_field_definitions`
--

INSERT INTO `autobooks_unified_field_definitions` (`id`, `field_name`, `field_definition`, `is_active`, `created_at`, `updated_at`) VALUES
('1', 'serial_number', '{\"label\":\"Serial Number\",\"type\":\"string\",\"category\":\"identification\",\"description\":\"Product or license serial number\",\"patterns\":[\"serial\\r\",\"serial_number\\r\",\"serial_no\\r\",\"product_serial\\r\",\"license_serial\"],\"normalized_fields\":[\"serial_number\\r\",\"product_serial\\r\",\"license_serial\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 09:28:01', '2025-08-16 09:35:55'),
('2', 'start_date', '{\"label\":\"Start Date\",\"type\":\"date\",\"category\":\"dates\",\"description\":\"Subscription or agreement start date\",\"patterns\":[\"start_date\\r\",\"date_start\\r\",\"purchase_date\\r\",\"agreement_start_date\\r\",\"subscription_start_date\\r\",\"activation_date\\r\",\"begin_date\\r\",\"subs_startDate\"],\"normalized_fields\":[\"start_date\\r\",\"subs_startDate\\r\",\"agreement_start_date\"],\"validation\":{\"required\":false},\"matching\":{\"enabled\":true,\"priority\":10,\"case_sensitive\":false,\"fuzzy_matching\":false,\"similarity_threshold\":70,\"exact_match_only\":true}}', '1', '2025-08-16 09:36:30', '2025-08-16 09:36:30');

COMMIT;
