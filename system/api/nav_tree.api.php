<?php
namespace api\nav_tree;

use system\users;
use edge\Edge;
use const icons\ICONS;
use system\database;
use system\data_importer;
use system\hilt;

/**
 * Display the form for adding a new navigation entry
 */
function add_nav_entry($p) {
    // Ensure only admin/dev can modify navigation
    users::requireRole(['admin', 'dev']);

    // Get the parent path from the request
    $parent_path = (!isset($p['parent_path']) || $p['parent_path'] == '') ? 'root' : $p['parent_path'];

    // Render the modal form for adding a new navigation entry
    return Edge::render('nav-entry-form', [
        'parent_path' => $parent_path,
        'icons' => ICONS
    ]);
}

function get_view_filename($parent_path, $filename): string{

    // Create view directory structure based on new file_path logic
    // file_path is the parent_path (empty if root)
    $file_path = ($parent_path == 'root') ? '' : $parent_path;

    // Build directory path: <FS_VIEWS>/<file_path>/<filename>/
    $view_dir = '/' . normalize_path(FS_VIEWS . '/' . $file_path . '/' . $filename) . '/';

    // Create directory if it doesn't exist
    if (!file_exists($view_dir)) {
        mkdir($view_dir, 0755, true);
    }

    // Create view file path: <FS_VIEWS>/<file_path>/<filename>/<filename>.edge.php
    return $view_dir . $filename . '.edge.php';
}
/**
 * Save a new navigation entry to the database
 */
function save_nav_entry($p) {
    print_rr($p,'begin save_nav_entry input_params');
    print_rr($_FILES,'$_FILES contents');
    // Ensure only admin/dev can modify navigation
    users::requireRole(['admin', 'dev']);

    // Get form data
    $parent_path = (isset($p['parent_path']) && $p['parent_path'] != '') ? $p['parent_path'] : 'root';
    $key = $p['key'] ?? '';
    $name = $p['name'] ?? '';
    $icon = $p['icon'] ?? 'document';
    $required_roles = $p['required_roles'] ?? [];
    $template = $p['template'] ?? 'none';
    $template_type = $p['template_type'] ?? '';
    $template_data = $p['template_data'] ?? [];

    // Validate required fields
    if (empty($key) || empty($name)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Key and name are required"}}');
        return;
    }

    // Handle file uploads immediately and store permanent paths
    $processed_files = [];
    if (!empty($_FILES)) {
        foreach ($_FILES as $field_name => $file_info) {
            if ($file_info['error'] === UPLOAD_ERR_OK) {
                // Create uploads directory if it doesn't exist
                if (!file_exists(FS_UPLOADS)) {
                    mkdir(FS_UPLOADS, 0755, true);
                }

                // Generate unique filename
                $file_extension = pathinfo($file_info['name'], PATHINFO_EXTENSION);
                $safe_filename = $key . '_' . $field_name . '_' . time() . '.' . $file_extension;
                $permanent_path = FS_UPLOADS . $safe_filename;

                // Move uploaded file to permanent location
                if (move_uploaded_file($file_info['tmp_name'], $permanent_path)) {
                    $processed_files[$field_name] = [
                        'name' => $file_info['name'],
                        'type' => $file_info['type'],
                        'size' => $file_info['size'],
                        'path' => $permanent_path,
                        'original_name' => $file_info['name']
                    ];
                    tcs_log("File uploaded successfully: {$permanent_path}", 'navigation');
                } else {
                    tcs_log("Failed to move uploaded file: {$file_info['name']}", 'navigation_errors');
                }
            }
        }
    }

    // Store form data in session for processing
    $_SESSION['nav_entry_data'] = [
        'parent_path' => $parent_path,
        'key' => $key,
        'name' => $name,
        'icon' => $icon,
        'required_roles' => $required_roles,
        'template' => $template,
        'template_type' => $template_type,
        'template_data' => $template_data,
        'files' => $processed_files,
        'progress' => 0,
        'status' => 'running',
        'start_time' => time()
    ];

    // Return initial progress bar
    return Edge::render('htmx-progress-bar', [
        'progress_id' => 'nav_entry_progress',
        'status_text' => 'Processing navigation entry...',
        'progress_endpoint' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/nav_tree/save_nav_entry_progress',
        'complete_endpoint' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/nav_tree/save_nav_entry_complete',
        'initial_progress' => 0,
        'status' => 'running'
    ]);
}

/**
 * Get progress of navigation entry creation
 */
function save_nav_entry_progress($p) {
    users::requireRole(['admin', 'dev']);

    if (!isset($_SESSION['nav_entry_data'])) {
        return '<div class="text-red-600">No active process found</div>';
    }

    $data = $_SESSION['nav_entry_data'];
    $elapsed = time() - $data['start_time'];

    print_rr($data,'save_nav_entry_progress data');

    // Check if the async process has completed
    if ($data['status'] === 'complete') {
        $progress = 100;
        $status_message = 'Complete!';
        header('HX-Trigger: done');
        $status = 'complete';
    } else {
        // Simulate progress based on elapsed time while process is running
        if ($elapsed < 1) {
            $progress = 10;
            $status_message = 'Validating form data...';
            $status = 'running';
        } elseif ($elapsed < 2) {
            $progress = 30;
            $status_message = 'Creating view file...';
            // Actually start processing in background
            process_nav_entry_async();
            $status = 'running';
        } elseif ($elapsed < 3) {
            $progress = 60;
            $status_message = 'Inserting into database...';
            $status = 'running';
        } else {
            $progress = 85;
            $status_message = 'Finalizing entry...';
            $status = 'running';
        }
    }
    $_SESSION['nav_entry_data']['progress'] = $progress;
    return Edge::render('htmx-progress-bar', [
        'progress_id' => 'nav_entry_progress',
        'status_text' => $status_message,
        'progress_endpoint' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/nav_tree/save_nav_entry_progress',
        'complete_endpoint' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/nav_tree/save_nav_entry_complete',
        'initial_progress' => $progress,
        'status' => $status ?? 'running'
    ]);

//    return '<div class="progress bg-gray-200 rounded-full h-2" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="' . $progress . '" aria-labelledby="nav_entry_progress_label">
//                <div id="nav_entry_progress_bar" class="progress-bar bg-indigo-600 h-full rounded-full transition-all duration-300 ease-out" style="width:' . $progress . '%"></div>
//            </div>';
}

/**
 * Get the nav-tree HTML for oob-swap
 */
function get_nav_tree_html() {
    // Get the navigation items (same as ROUTE_TREE)
    $routes = get_navigation_items();

    // Render the nav-tree component
    return Edge::render('nav-tree', [
        'items' => $routes
    ]);
}

/**
 * Handle completion of navigation entry creation
 */
function save_nav_entry_complete($p) {
    users::requireRole(['admin', 'dev']);

    if (!isset($_SESSION['nav_entry_data'])) {
        return '<div class="text-red-600">No process data found</div>';
    }

    $data = $_SESSION['nav_entry_data'];

    // Check if there was an error during processing
    if (isset($data['error'])) {
        $error_message = $data['error'];

        // Handle duplicate key error specially
        if ($error_message === 'duplicate_key' && isset($data['existing_entry'])) {
            $existing_entry = $data['existing_entry'];

            // Don't clean up session data yet - we need it for the update operation
            return Edge::render('nav-entry-duplicate-options', [
                'existing_entry' => $existing_entry,
                'new_entry_data' => $data,
                'progress_id' => 'nav_entry_progress'
            ]);
        }


        // Handle table exists error specially
        if ($error_message === 'table_exists' && isset($data['table_error_message'])) {
            $table_error = $data['table_error_message'];

            // Don't clean up session data yet - we need it for the update operation
            return Edge::render('nav-entry-table-exists-options', [
                'table_error_message' => $table_error,
                'new_entry_data' => $data,
                'progress_id' => 'nav_entry_progress'
            ]);
        }

        // Clean up any uploaded files on error
        if (isset($data['files'])) {
            cleanup_uploaded_files($data['files']);
        }

        unset($_SESSION['nav_entry_data']);
        return Edge::render('htmx-progress-bar', [
            'progress_id' => 'nav_entry_progress',
            'status_text' => 'Error: ' . $error_message,
            'progress_endpoint' => '',
            'complete_endpoint' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/nav_tree/add_nav_entry',
            'initial_progress' => 0,
            'status' => 'error'
        ]);
    }

    // Success - determine operation type
    $operation = $data['operation'] ?? 'created';
    $entry_name = $data['name'];
    unset($_SESSION['nav_entry_data']);

    // Set appropriate success message based on operation
    $success_message = $operation === 'updated'
        ? "Navigation entry updated successfully"
        : "Navigation entry added successfully";

    $status_text = $operation === 'updated'
        ? 'Navigation entry "' . $entry_name . '" updated successfully!'
        : 'Navigation entry "' . $entry_name . '" created successfully!';

    // Set a header to trigger redirect after showing complete state briefly
    header('HX-Trigger: {"showNotification": {"type": "success", "message": "' . $success_message . '"}, "redirectAfterDelay": {"url": "' . APP_ROOT . '", "delay": 2000}}');

    // Get the updated nav-tree HTML for oob-swap
    $nav_tree_html = get_nav_tree_html();

    // Return complete state with oob-swap for nav-tree
    $progress_html = Edge::render('htmx-progress-bar', [
        'progress_id' => 'nav_entry_progress',
        'status_text' => $status_text,
        'progress_endpoint' => '',
        'complete_endpoint' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/nav_tree/add_nav_entry',
        'initial_progress' => 100,
        'status' => 'complete'
    ]);

    // Add the nav-tree oob-swap by modifying the nav-tree HTML to include the oob-swap attribute
    $nav_tree_with_oob = str_replace('<ul', '<ul id="main-nav-tree" hx-swap-oob="true"', $nav_tree_html);

    return $progress_html . $nav_tree_with_oob;
}

/**
 * Process navigation entry creation asynchronously
 */
function process_nav_entry_async() {
    if (!isset($_SESSION['nav_entry_data']) || $_SESSION['nav_entry_data']['status'] !== 'running') {
        return;
    }

    $data = $_SESSION['nav_entry_data'];

    try {
        // Create view file path
        $view_file = get_view_filename($data['parent_path'], $data['key']);

        // Generate view content based on template
        $is_update = $data['is_update'] ?? false;
        $view_content = generate_view_content(
            $data['template'],
            $data['template_data'],
            $data['template_type'],
            $data['files'],
            $data['key'],
            $data['name'],
            $is_update
        );

        // Check if this is a table exists error
        if (is_string($view_content) && strpos($view_content, 'TABLE_EXISTS_ERROR:') === 0) {
            $error_message = substr($view_content, strlen('TABLE_EXISTS_ERROR:'));

            if (!$is_update) {
                // Table exists but this is not an update - offer update option
                $_SESSION['nav_entry_data']['error'] = 'table_exists';
                $_SESSION['nav_entry_data']['table_error_message'] = $error_message;
                return;
            } else {
                // This shouldn't happen in update mode, but handle it gracefully
                $_SESSION['nav_entry_data']['error'] = 'Table update failed: ' . $error_message;
                return;
            }
        }

        // Write view file
        if (!file_put_contents($view_file, $view_content)) {
            $_SESSION['nav_entry_data']['error'] = 'Failed to create view file';
            return;
        }

        tcs_log("View file {$view_file} created successfully", 'navigation');

        // Convert required_roles array to JSON for storage
        $required_roles_json = json_encode($data['required_roles']);

        // Calculate file_path based on parent_path and route_key
        $file_path = ($data['parent_path'] == 'root') ? '' : $data['parent_path'];
        $is_system = false; // User-created entries are not system entries

        // Check if entry already exists
        $existing_entry = database::table('autobooks_navigation')
            ->select(['id', 'name'])
            ->where('parent_path', $data['parent_path'])
            ->where('route_key', $data['key'])
            ->first();

        if ($existing_entry) {
            // Entry exists - check if this is an update operation
            $is_update = $data['is_update'] ?? false;

            if ($is_update) {
                // Update existing entry
                $result = database::table('autobooks_navigation')
                    ->where('parent_path', $data['parent_path'])
                    ->where('route_key', $data['key'])
                    ->update([
                        'name' => $data['name'],
                        'icon' => $data['icon'],
                        'required_roles' => $required_roles_json,
                        'file_path' => $file_path,
                        'is_system' => $is_system,
                        'can_delete' => true,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                if ($result) {
                    // Log successful navigation entry update
                    if (function_exists('tcs_log')) {
                        tcs_log([
                            'action' => 'navigation_entry_updated',
                            'template' => $data['template'],
                            'template_data' => $data['template_data'],
                            'parent_path' => $data['parent_path'],
                            'route_key' => $data['key'],
                            'name' => $data['name'],
                            'icon' => $data['icon'],
                            'required_roles' => $data['required_roles'],
                            'user_id' => $_SESSION['user_id'] ?? 'Unknown',
                            'existing_name' => $existing_entry['name']
                        ], 'navigation');
                    }

                    $_SESSION['nav_entry_data']['status'] = 'complete';
                    $_SESSION['nav_entry_data']['operation'] = 'updated';
                } else {
                    $_SESSION['nav_entry_data']['error'] = 'Failed to update navigation entry in database';
                }
            } else {
                // Entry exists but this is not an update - offer update option
                $_SESSION['nav_entry_data']['error'] = 'duplicate_key';
                $_SESSION['nav_entry_data']['existing_entry'] = $existing_entry;
            }
        } else {
            // Get the maximum sort_order for this parent_path to add new entry at the bottom
            $max_sort_order = database::table('autobooks_navigation')
                ->where('parent_path', $data['parent_path'])
                ->max('sort_order') ?? 0;

            // Insert new entry
            $result = database::table('autobooks_navigation')->insert([
                'parent_path' => $data['parent_path'],
                'route_key' => $data['key'],
                'name' => $data['name'],
                'icon' => $data['icon'],
                'required_roles' => $required_roles_json,
                'file_path' => $file_path,
                'is_system' => $is_system,
                'can_delete' => true,
                'sort_order' => $max_sort_order + 1
            ]);

            if ($result) {
                // Log successful navigation entry creation
                if (function_exists('tcs_log')) {
                    tcs_log([
                        'action' => 'navigation_entry_created',
                        'template' => $data['template'],
                        'template_data' => $data['template_data'],
                        'parent_path' => $data['parent_path'],
                        'route_key' => $data['key'],
                        'name' => $data['name'],
                        'icon' => $data['icon'],
                        'required_roles' => $data['required_roles'],
                        'user_id' => $_SESSION['user_id'] ?? 'Unknown',
                        'result' => $result
                    ], 'navigation');
                }

                $_SESSION['nav_entry_data']['status'] = 'complete';
                $_SESSION['nav_entry_data']['operation'] = 'created';
            } else {
                $_SESSION['nav_entry_data']['error'] = 'Failed to add navigation entry to database';
            }
        }

    } catch (\system\DatabaseException $e) {
        // Check if this is a duplicate key error
        if (strpos($e->getMessage(), 'Duplicate entry') !== false && strpos($e->getMessage(), 'unique_route') !== false) {
            // Extract existing entry information for user-friendly error
            try {
                $existing_entry = database::table('autobooks_navigation')
                    ->select(['id', 'name'])
                    ->where('parent_path', $data['parent_path'])
                    ->where('route_key', $data['key'])
                    ->first();

                $_SESSION['nav_entry_data']['error'] = 'duplicate_key';
                $_SESSION['nav_entry_data']['existing_entry'] = $existing_entry;
            } catch (\Exception $lookup_error) {
                $_SESSION['nav_entry_data']['error'] = 'Navigation entry with this key already exists';
            }
        } else {
            $_SESSION['nav_entry_data']['error'] = 'Database error: ' . $e->getMessage();
        }

        tcs_log([
            'action' => 'navigation_entry_creation_failed',
            'error_type' => 'DatabaseException',
            'error_message' => $e->getMessage(),
            'query' => $e->getQuery(),
            'parameters' => $e->getParams()
        ], 'navigation_errors');

    } catch (\Exception $e) {
        $_SESSION['nav_entry_data']['error'] = 'Unexpected error: ' . $e->getMessage();
        tcs_log([
            'action' => 'navigation_entry_creation_failed',
            'error_type' => 'Exception',
            'error_message' => $e->getMessage()
        ], 'navigation_errors');
    }
}

/**
 * Handle user's choice when duplicate key is found
 */
function handle_duplicate_choice($p) {
    users::requireRole(['admin', 'dev']);

    if (!isset($_SESSION['nav_entry_data'])) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Session expired. Please try again."}}');
        return;
    }

    $choice = $p['choice'] ?? '';

    if ($choice === 'update') {
        // User chose to update - set flag and restart processing
        $_SESSION['nav_entry_data']['is_update'] = true;
        $_SESSION['nav_entry_data']['status'] = 'running';
        $_SESSION['nav_entry_data']['start_time'] = time();
        unset($_SESSION['nav_entry_data']['error']);
        unset($_SESSION['nav_entry_data']['existing_entry']);

        // Return progress bar to restart the process
        return Edge::render('htmx-progress-bar', [
            'progress_id' => 'nav_entry_progress',
            'status_text' => 'Updating existing navigation entry...',
            'progress_endpoint' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/nav_tree/save_nav_entry_progress',
            'complete_endpoint' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/nav_tree/save_nav_entry_complete',
            'initial_progress' => 0,
            'status' => 'running'
        ]);
    } elseif ($choice === 'cancel') {
        // User chose to cancel - clean up and return to form
        if (isset($_SESSION['nav_entry_data']['files'])) {
            cleanup_uploaded_files($_SESSION['nav_entry_data']['files']);
        }

        unset($_SESSION['nav_entry_data']);
        header('HX-Trigger: {"showNotification": {"type": "info", "message": "Operation cancelled"}}');

        return Edge::render('htmx-progress-bar', [
            'progress_id' => 'nav_entry_progress',
            'status_text' => 'Operation cancelled',
            'progress_endpoint' => '',
            'complete_endpoint' => APP_ROOT . DIRECTORY_SEPARATOR . 'api/nav_tree/add_nav_entry',
            'initial_progress' => 0,
            'status' => 'cancelled'
        ]);
    } else {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Invalid choice"}}');
        return;
    }
}

/**
 * Clean up uploaded files when there's an error
 */
function cleanup_uploaded_files($files) {
    if (!empty($files)) {
        foreach ($files as $file_info) {
            if (isset($file_info['path']) && file_exists($file_info['path'])) {
                unlink($file_info['path']);
                tcs_log("Cleaned up uploaded file: {$file_info['path']}", 'navigation');
            }
        }
    }
}

/**
 * Generate view content based on template type and data
 *
 * @param string $template Template type
 * @param array $template_data Template data from form
 * @param array $files Uploaded files
 * @param string $key Route key
 * @param bool $is_update Whether this is an update operation
 * @return string Generated view content
 */
function generate_view_content($template, $template_data, $template_type, $files, $key,$name, $is_update = false) {
    // Template directory
    $template_dir = FS_SYS_TEMPLATES;
    print_rr($template,'template');

    // All templates now use the hilt system
    $template_file = "{$template_dir}" . DS .  "{$template}.hilt.php";

    // Map old template types to hilt template types
    $hilt_template_type = match($template_type) {
        'csv' => 'database',
        'custom_html' => 'html',
        'file_upload' => 'file_upload',
        'none', 'default' => 'default',
        default => $template_type
    };

    if (!file_exists($template_file)) {
        // Create default hilt template if it doesn't exist
        hilt::create_default_hilt_template($template_file, $hilt_template_type);
    }

    tcs_log([
        'template_file' => $template_file,
        'template_data' => $template_data,
        'files' => $files,
        'key' => $key,
        'template_type' => $template_type,
        'hilt_template_type' => $hilt_template_type
    ], 'navigation');

    return hilt::process_template($template_file, $template_data, $files, $key, $name, $hilt_template_type, $is_update);
}


/**
 * Process data table template with CSV data using data_importer class
 *
 * Uses the data_importer class for robust CSV parsing and error handling.
 * Supports both textarea input and file uploads.
 *
 * @param string $template_file Template file path
 * @param array $template_data Template data from form
 * @param array $files Uploaded files
 * @param string $key Route key
 * @return string Generated view content
 */
function process_data_table_template($template_file, $template_data, $files, $key) {
    $csv_data = '';
    $csv_file_path = '';

    $csv_file_path = FS_UPLOADS . '/' . $key . '_data.csv';
    print_rr([
        'csv_file_path' => $csv_file_path,
        'files' => $files,
        'key' => $key
    ],'csv_file_path');
    // Process CSV data from textarea
    if (!empty($template_data['csv'])) {
        $csv_data = $template_data['csv'];
        
        // Save CSV to file for future reference
        
        file_put_contents($csv_file_path, $csv_data);
    }
    // Process uploaded CSV file (now using processed file paths)
    elseif (isset($files['csv_file']) && isset($files['csv_file']['path'])) {
        $csv_file_path = $files['csv_file']['path'];

        if (file_exists($csv_file_path)) {
            $csv_data = file_get_contents($csv_file_path);
            tcs_log("Using uploaded CSV file: {$csv_file_path}", 'navigation');
        } else {
            tcs_log("Uploaded CSV file not found: {$csv_file_path}", 'navigation_errors');
        }
    }
    
    // Parse CSV data using data_importer class
    $headers = [];
    $rows = [];

    print_rr([
        'csv_data_length' => strlen($csv_data),
        'csv_file_path' => $csv_file_path,
        'csv_data_preview' => substr($csv_data, 0, 200)
    ], 'CSV processing debug');

    if (!empty($csv_data)) {
        // Use data_importer for robust CSV parsing
        if (!empty($csv_file_path) && file_exists($csv_file_path)) {
            // Use file-based parsing for uploaded files
            $csv_result = data_importer::read_csv_file($csv_file_path);
        } else {
            // For textarea data, create a temporary file
            $temp_file = tempnam(sys_get_temp_dir(), 'nav_tree_csv_');
            file_put_contents($temp_file, $csv_data);
            $csv_result = data_importer::read_csv_file($temp_file);
            unlink($temp_file); // Clean up temporary file
        }

        // Handle parsing results
        if (isset($csv_result['error'])) {
            // Log the error for debugging
            if (function_exists('tcs_log')) {
                tcs_log([
                    'action' => 'csv_parsing_failed',
                    'error' => $csv_result['error'],
                    'key' => $key,
                    'csv_file_path' => $csv_file_path ?? 'textarea_data'
                ], 'nav_tree_template');
            }
            // Fall back to empty data rather than breaking
            $headers = [];
            $rows = [];
        } else {
            $headers = $csv_result['header'] ?? [];
            $rows = $csv_result['rows'] ?? [];

            // Log successful parsing
            if (function_exists('tcs_log')) {
                tcs_log([
                    'action' => 'csv_parsing_successful',
                    'key' => $key,
                    'headers_count' => count($headers),
                    'rows_count' => count($rows),
                    'csv_file_path' => $csv_file_path ?? 'textarea_data'
                ], 'nav_tree_template');
            }
        }
    }

    print_rr([
        'headers' => $headers,
        'rows_count' => count($rows),
        'first_row' => isset($rows[0]) ? $rows[0] : null
    ], 'Final CSV parsing result');

    // Get template content
    $template_content = file_get_contents($template_file);
    
    // Replace placeholders with actual data
    $template_content = str_replace('{{ $headers }}', preg_replace('/(([^)],)\s+|( =>)\s+( array \()\s+)/s', '$2$3$4 ', var_export($headers, true)), $template_content);
    $template_content = str_replace('{{ $rows }}', preg_replace('/(([^)],)\s+|( =>)\s+( array \()\s+)/s', '$2$3$4 ', var_export($rows, true)), $template_content);

    return $template_content;
}

/**
 * Create a default custom HTML template
 * 
 * @param string $template_file Path to create the template
 */
function create_default_custom_html_template($template_file) {
    // Create directory if it doesn't exist
    $dir = dirname($template_file);
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
    
    $template_content = <<<'EOT'
<?php
// Custom HTML template
// This template will be used to generate custom HTML views

// The HTML content will be replaced with actual content
?>
/* HTML_CONTENT */
EOT;
    
    file_put_contents($template_file, $template_content);
}

/**
 * Process custom HTML template
 * 
 * @param string $template_file Template file path
 * @param array $template_data Template data from form
 * @return string Generated view content
 */
function process_custom_html_template($template_file, $template_data) {
    $html = $template_data['html'] ?? '';
    
    // Get template content
    $template_content = file_get_contents($template_file);
    
    // Replace placeholder with actual HTML
    $template_content = str_replace('/* HTML_CONTENT */', $html, $template_content);
    
    return $template_content;
}

/**
 * Process file upload as template
 * 
 * @param array $files Uploaded files
 * @param string $key Route key
 * @return string Generated view content
 */
function process_file_upload_template($files, $key) {
    if (isset($files['template_file']) && isset($files['template_file']['path'])) {
        $upload_path = $files['template_file']['path'];

        if (file_exists($upload_path)) {
            $file_content = file_get_contents($upload_path);
            $extension = pathinfo($upload_path, PATHINFO_EXTENSION);

            tcs_log("Processing uploaded template file: {$upload_path}", 'navigation');
            
            if ($extension === 'php') {
                // For PHP files, use the content directly
                return $file_content;
            } elseif ($extension === 'html') {
                // For HTML files, wrap in PHP tags
                return "<?php\n// Auto-generated view from uploaded HTML file\n?>\n" . $file_content;
            } else {
                // For other files, create a PHP file that outputs the content
                return "<?php\n// Auto-generated view from uploaded file\necho file_get_contents('" . $upload_path . "');\n";
            }
        }
    }
    
    // Default content if file upload failed
    return "<?php\n// Auto-generated view (file upload failed)\necho '<div class=\"p-4 text-red-500\">No template file was uploaded or upload failed</div>';\n";
}

/**
 * Create a default template
 * 
 * @param string $template_file Path to create the template
 */
function create_default_template($template_file) {
    // Create directory if it doesn't exist
    $dir = dirname($template_file);
    if (!file_exists($dir)) {
        mkdir($dir, 0755, true);
    }
    
    $template_content = <<<'EOT'
        <?php
            // Default template
            // This template will be used to generate default views
            
            echo '<div class="p-4">';
            echo '<h1 class="text-2xl font-bold mb-4">Welcome to /* PAGE_KEY */</h1>';
            echo '<p>This is a default view. Edit this file to customize the content.</p>';
            echo '</div>';
    EOT;
    
    file_put_contents($template_file, $template_content);
}

/**
 * Process default template
 *
 * @param string $template_file Template file path
 * @param string $key Route key
 * @return string Generated view content
 */
function process_default_template($template_file, $key) {
    // Get template content
    $template_content = file_get_contents($template_file);

    // Replace placeholder with actual key
    $template_content = str_replace('/* PAGE_KEY */', $key, $template_content);

    return $template_content;
}



/**
 * Delete a navigation entry from the database and filesystem
 */
function delete_nav_entry($p) {
    // Ensure only admin/dev can modify navigation
    users::requireRole(['admin', 'dev']);
    print_rr($p,'POST_params');
    print_rr($_GET,'GET_params');
    print_rr($p,'begin delete_nav_entry input_params');
    // Get form data
    $parent_path = (!isset($p['parent_path']) || $p['parent_path'] == '') ? 'root' : $p['parent_path'];
    $key = $p['key'] ?? '';
    $parent_path = $p['parent_path'] == '' ? 'root' : $p['parent_path'];
    print_rr($parent_path,'parent_path');
    print_rr($key,'key');
    // Validate required fields
    if (empty($key)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Route key is required"}}');
        return;
    }
    print_rr($parent_path,'parent_path');
    $parent_path_db = $parent_path == '' ? 'root' : $parent_path;
    try {
        // First, get the navigation entry to confirm it exists
        $entry = database::table('autobooks_navigation')
            ->select(['id', 'name', 'route_key'])
            ->where('parent_path', $parent_path_db)
            ->where('route_key', $key)
            ->first();

        print_rr($entry,'entry');
        if (!$entry) {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Navigation entry not found"}}');
            print_rr($parent_path,'Navigation entry not found');
            return;
        }
        // Prepare cleanup operations
        $data_table_name = 'autobooks_import_' . $key . '_data';
        $view_folder_path = dirname(get_view_filename($parent_path == 'root' ? '' : $parent_path, $key));

        print_rr([
            'data_table_name' => $data_table_name,
            'table_exists' => database::tableExists($data_table_name),
            'view_folder_path' => $view_folder_path,
            'folder_exists' => is_dir($view_folder_path)
        ], 'cleanup_info');

        // Step 1: Delete from navigation table
        $nav_delete_result = database::table('autobooks_navigation')
            ->where('parent_path', $parent_path_db)
            ->where('route_key', $key)
            ->delete();

        print_rr($nav_delete_result, 'nav_delete_result');

        // Step 2: Drop data table if it exists
        $data_table_result = true; // Default to success
        if (database::tableExists($data_table_name)) {
            try {
                $data_table_result = database::execute("DROP TABLE `{$data_table_name}`");
                print_rr($data_table_result, 'data_table_drop_result');
            } catch (Exception $e) {
                print_rr($e->getMessage(), 'data_table_drop_error');
                $data_table_result = false;
            }
        }
        // Step 2.5: Drop data source entry if it exists
        $data_source_result = true; // Default to success
        if (database::tableExists('autobooks_data_sources')) {
            // get data source id from config table
            $data_source_id = database::table('autobooks_table_configs')
                ->where('table_name', $data_table_name)
                ->first()['data_source_id'] ?? null;
            if ($data_source_id) {
                try {
                    $data_source_result = database::table('autobooks_data_sources')
                        ->where('id', $data_source_id)
                        ->delete();
                    print_rr($data_source_result, 'data_source_delete_result');
                } catch (Exception $e) {
                    print_rr($e->getMessage(), 'data_source_delete_error');
                    $data_source_result = false;
                }
            }
        }
        // Step 3: Remove table configuration if it exists
        $config_delete_result = true; // Default to success
        if (database::tableExists('autobooks_table_configs')) {
            try {
                $config_delete_result = database::table('autobooks_table_configs')
                    ->where('table_name', $data_table_name)
                    ->delete();
                print_rr($config_delete_result, 'config_delete_result');
            } catch (Exception $e) {
                print_rr($e->getMessage(), 'config_delete_error');
                $config_delete_result = false;
            }
        }

        // Step 4: Delete associated data sources
        $data_source_delete_result = false;
        try {
            $deleted_sources = database::table('autobooks_data_sources')
                ->where('table_name', $data_table_name)
                ->delete();
            $data_source_delete_result = true;
            print_rr(['deleted_data_sources' => $deleted_sources], 'data_source_cleanup');
        } catch (Exception $e) {
            $errors[] = "Failed to delete data sources for table: {$data_table_name} - " . $e->getMessage();
            print_rr(['data_source_delete_error' => $e->getMessage()], 'data_source_cleanup_error');
        }

        // Step 5: Delete data table storage entries (both default and user-specific)
        $storage_delete_result = false;
        try {
            $deleted_storage = database::table('autobooks_data_table_storage')
                ->where('table_name', $data_table_name)
                ->delete();
            $storage_delete_result = true;
            print_rr(['deleted_storage_entries' => $deleted_storage], 'storage_cleanup');
        } catch (Exception $e) {
            $errors[] = "Failed to delete data table storage entries for table: {$data_table_name} - " . $e->getMessage();
            print_rr(['storage_delete_error' => $e->getMessage()], 'storage_cleanup_error');
        }

        // Step 6: Delete view folder and all contents
        $folder_delete_result = true; // Default to success
        if (is_dir($view_folder_path)) {
            try {
                // Use deltree with 'views' target for security
                $folder_delete_result = deltree($view_folder_path, 'views');
                print_rr($folder_delete_result, 'folder_delete_result');

                // If deltree succeeded, also remove the empty parent directory
                if ($folder_delete_result) {
                    @rmdir($view_folder_path);
                }
            } catch (Exception $e) {
                print_rr($e->getMessage(), 'folder_delete_error');
                $folder_delete_result = false;
            }
        }

        // Step 7: Clean up any uploaded CSV files associated with this entry
        $csv_cleanup_result = true; // Default to success
        try {
            // Look for CSV files that match the pattern: {key}_csv_file_*.csv
            $uploads_pattern = FS_UPLOADS . $key . '_csv_file_*.csv';
            $csv_files = glob($uploads_pattern);

            if (!empty($csv_files)) {
                foreach ($csv_files as $csv_file) {
                    if (file_exists($csv_file)) {
                        $unlink_result = @unlink($csv_file);
                        if (!$unlink_result) {
                            $csv_cleanup_result = false;
                        }
                        print_rr(['file' => $csv_file, 'deleted' => $unlink_result], 'csv_file_cleanup');
                    }
                }
            }
        } catch (Exception $e) {
            print_rr($e->getMessage(), 'csv_cleanup_error');
            $csv_cleanup_result = false;
        }

        // Determine overall success
        $overall_success = $nav_delete_result && $data_table_result && $config_delete_result && $data_source_delete_result && $storage_delete_result && $folder_delete_result && $csv_cleanup_result;

        // Collect any errors
        $errors = [];
        if (!$nav_delete_result) $errors[] = 'navigation entry';
        if (!$data_table_result) $errors[] = 'data table';
        if (!$config_delete_result) $errors[] = 'table configuration';
        if (!$data_source_delete_result) $errors[] = 'data sources';
        if (!$storage_delete_result) $errors[] = 'data table storage';
        if (!$folder_delete_result) $errors[] = 'view folder';
        if (!$csv_cleanup_result) $errors[] = 'uploaded files';

        // Log the deletion attempt
        if (function_exists('tcs_log')) {
            tcs_log([
                'action' => 'navigation_entry_deletion_attempt',
                'success' => $overall_success,
                'parent_path' => $parent_path,
                'route_key' => $key,
                'name' => $entry['name'],
                'nav_delete' => $nav_delete_result,
                'data_table_delete' => $data_table_result,
                'config_delete' => $config_delete_result,
                'data_source_delete' => $data_source_delete_result,
                'storage_delete' => $storage_delete_result,
                'folder_delete' => $folder_delete_result,
                'csv_cleanup' => $csv_cleanup_result,
                'errors' => $errors,
                'user_id' => $_SESSION['user_id'] ?? 'Unknown'
            ], 'navigation');
        }

        // Send appropriate response
        if ($overall_success) {
            header('HX-Trigger: {"showNotification": {"type": "success", "message": "Navigation entry and all associated data deleted successfully"}}');
        } else {
            $error_message = 'Failed to delete: ' . implode(', ', $errors);
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "' . $error_message . '"}}');
        }

        print_rr([
            'overall_success' => $overall_success,
            'errors' => $errors,
            'nav_delete' => $nav_delete_result,
            'data_table_delete' => $data_table_result,
            'config_delete' => $config_delete_result,
            'data_source_delete' => $data_source_delete_result,
            'storage_delete' => $storage_delete_result,
            'folder_delete' => $folder_delete_result,
            'csv_cleanup' => $csv_cleanup_result
        ], 'final_result');
        // Redirect to refresh the navigation
        //header('HX-Redirect: ' . APP_ROOT);

    } catch (\system\DatabaseException $e) {
        // Handle database-specific errors with detailed logging
        $errorData = [
            'action' => 'navigation_entry_deletion_failed',
            'error_type' => 'DatabaseException',
            'error_message' => $e->getMessage(),
            'query' => $e->getQuery(),
            'parameters' => $e->getParams(),
            'input_data' => [
                'parent_path' => $parent_path,
                'route_key' => $key
            ],
            'user_id' => $_SESSION['user_id'] ?? 'Unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];

        tcs_log($errorData, 'navigation_errors');

        // Return appropriate error response
        if (defined('API_RUN') && API_RUN) {
            // For API calls, return JSON error
            header('Content-Type: application/json');
            echo json_encode([
                'error' => 'Database error occurred while deleting navigation entry',
                'details' => defined('DEBUG_MODE') && DEBUG_MODE ? $e->getDetailedMessage() : 'Please contact support',
                'error_id' => uniqid()
            ]);
        } else {
            // For web requests, show user-friendly error
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Database error: ' .
                   (defined('DEBUG_MODE') && DEBUG_MODE ? $e->getMessage() : 'Please try again or contact support') . '"}}');
        }
        return;

    } catch (\Exception $e) {
        // Handle any other unexpected errors
        $errorData = [
            'action' => 'navigation_entry_deletion_failed',
            'error_type' => 'GeneralException',
            'error_message' => $e->getMessage(),
            'input_data' => [
                'parent_path' => $parent_path,
                'route_key' => $key
            ],
            'user_id' => $_SESSION['user_id'] ?? 'Unknown',
            'timestamp' => date('Y-m-d H:i:s'),
            'stack_trace' => $e->getTraceAsString()
        ];
        tcs_log($errorData, 'navigation_errors');
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "An unexpected error occurred. Please try again."}}');

        return;
    }
}

/**
 * Reorder navigation items within the same parent
 */
function reorder_navigation($p) {
    // Ensure only admin/dev can modify navigation
    users::requireRole(['admin', 'dev']);

    $parent_path = $p['parent_path'] ?? 'root';

    // Handle both old JSON format and new form array format
    if (isset($p['items']) && is_string($p['items'])) {
        // Old JSON format
        $items = json_decode($p['items'], true);
        if (empty($items)) {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "No items to reorder"}}');
            return;
        }

        try {
            // Update sort_order for each item
            foreach ($items as $item) {
                database::table('autobooks_navigation')
                    ->where('route_key', $item['route_key'])
                    ->where('parent_path', $parent_path)
                    ->update(['sort_order' => $item['sort_order']]);
            }
        } catch (Exception $e) {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to reorder navigation"}}');
            return;
        }
    } else if (isset($p['item']) && is_array($p['item'])) {
        // New form array format - items are in the order they appear in the form
        $item_keys = $p['item'];

        if (empty($item_keys)) {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "No items to reorder"}}');
            return;
        }

        try {
            // Update sort_order based on array position
            foreach ($item_keys as $index => $route_key) {
                database::table('autobooks_navigation')
                    ->where('route_key', $route_key)
                    ->where('parent_path', $parent_path)
                    ->update(['sort_order' => $index + 1]);
            }
        } catch (Exception $e) {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to reorder navigation"}}');
            return;
        }
    } else {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "No items to reorder"}}');
        return;
    }

    header('HX-Trigger: {"showNotification": {"type": "success", "message": "Navigation reordered successfully"}}');
    return '';

    } catch (\Exception $e) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to reorder navigation: ' . $e->getMessage() . '"}}');
        return;
    }
}

/**
 * Move navigation item to create/update subfolder
 */
function move_to_subfolder($p) {
    // Ensure only admin/dev can modify navigation
    users::requireRole(['admin', 'dev']);

    $item_key = $p['item_key'] ?? '';
    $current_parent = $p['current_parent'] ?? 'root';
    $target_parent = $p['target_parent'] ?? 'root';

    if (empty($item_key)) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Invalid move parameters"}}');
        return;
    }

    try {
        // The target_parent IS the new parent path
        $new_parent_path = $target_parent;

        // Prevent items from being parents of themselves
        if ($new_parent_path === $item_key) {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Cannot move item to be a parent of itself"}}');
            return;
        }

        // Prevent circular references by checking if the target is a descendant of the item being moved
        if (wouldCreateCircularReference($item_key, $new_parent_path)) {
            header('HX-Trigger: {"showNotification": {"type": "error", "message": "Cannot create circular reference in navigation"}}');
            return;
        }

        // Update the item's parent_path
        database::table('autobooks_navigation')
            ->where('route_key', $item_key)
            ->where('parent_path', $current_parent)
            ->update(['parent_path' => $new_parent_path]);

        header('HX-Trigger: {"showNotification": {"type": "success", "message": "Item moved to subfolder successfully"}}');
        return '';

    } catch (\Exception $e) {
        header('HX-Trigger: {"showNotification": {"type": "error", "message": "Failed to move item"}}');
        return;
    }
}

/**
 * Get navigation items from the database
 *
 * @param string $parent_path The parent path to get items for
 * @param array $visited_paths Track visited paths to prevent infinite recursion
 * @param int $depth Current recursion depth
 * @return array Navigation items
 */
function get_navigation_items($parent_path = '', $visited_paths = [], $depth = 0) {
    try {
        // Prevent infinite recursion
        if ($depth > 10) {
            tcs_log("Navigation recursion depth exceeded for path: {$parent_path}", 'navigation_errors');
            return [];
        }

        // Prevent circular references
        if (in_array($parent_path, $visited_paths)) {
            tcs_log("Circular reference detected in navigation path: {$parent_path}", 'navigation_errors');
            return [];
        }

        // Check if the navigation table exists
        if (!database::schema()::hasTable('autobooks_navigation')) {
            // Table doesn't exist yet, return empty array
            return [];
        }

        // Convert empty string to 'root' for database query
        $db_parent_path = empty($parent_path) ? 'root' : $parent_path;

        // Get navigation items for the given parent path - ordered by sort_order
        $results = database::table('autobooks_navigation')
            ->select(['route_key', 'name', 'icon', 'required_roles', 'file_path', 'is_system', 'can_delete'])
            ->where('parent_path', $db_parent_path)
            ->orderBy('sort_order', 'asc')
            ->orderBy('name', 'asc')
            ->get();

        $items = [];
        $new_visited_paths = array_merge($visited_paths, [$parent_path]);

        foreach ($results as $row) {
            $key = $row['route_key'];

            // Skip items that would create self-reference
            if ($key === $parent_path) {
                tcs_log("Skipping self-referencing navigation item: {$key}", 'navigation_errors');
                continue;
            }

            $items[$key] = [
                'name' => $row['name'],
                'icon' => $row['icon'],
                'required_roles' => json_decode($row['required_roles'], true) ?: [],
                'file_path' => $row['file_path'],
                'is_system' => (bool)$row['is_system']
            ];

            // Check for sub-items
            $sub_path = trim($parent_path . '/' . $key, '/');
            $sub_items = get_navigation_items($sub_path, $new_visited_paths, $depth + 1);
            if (!empty($sub_items)) {
                $items[$key]['sub_folder'] = $sub_items;
            }
        }

        return $items;

    } catch (\system\DatabaseException $e) {
        // Log database error but don't break the navigation
        if (function_exists('tcs_log')) {
            tcs_log([
                'action' => 'get_navigation_items_failed',
                'error_type' => 'DatabaseException',
                'error_message' => $e->getMessage(),
                'query' => $e->getQuery(),
                'parameters' => $e->getParams(),
                'parent_path' => $parent_path,
                'user_id' => $_SESSION['user_id'] ?? 'Unknown'
            ], 'navigation_errors');
        }

        // Return empty array to prevent breaking the navigation
        return [];

    } catch (\Exception $e) {
        // Log any other errors
        if (function_exists('tcs_log')) {
            tcs_log([
                'action' => 'get_navigation_items_failed',
                'error_type' => 'GeneralException',
                'error_message' => $e->getMessage(),
                'parent_path' => $parent_path,
                'user_id' => $_SESSION['user_id'] ?? 'Unknown',
                'stack_trace' => $e->getTraceAsString()
            ], 'navigation_errors');
        }

        // Return empty array to prevent breaking the navigation
        return [];
    }
}

/**
 * API endpoint to return template-specific form fields as Edge components
 * Called via HTMX when template selection changes
 */
function get_template_fields($p) {
    // Ensure only admin/dev can access this
    users::requireRole(['admin', 'dev']);
    print_rr($p,'get_template_fields input_params');
    // Get the template selection and extract the type
    $template_key = $p['template'] ?? 'default_template';

    // Map template keys to types based on the original form logic
    $template_type_map = [
        'default_template' => 'default',
        'custom_html_template' => 'custom_html',
        'file_upload_template' => 'file_upload'
    ];

    // Check if it's a CSV template (any template with 'csv' in the type)
    $template_type = 'default';
    if (isset($template_type_map[$template_key])) {
        $template_type = $template_type_map[$template_key];
    } else {
        print_rr($template_key,'template_key');
        // For dynamically loaded templates, we need to check the template file
        // This is a simplified approach - in production you might want to cache this
        $template_dir = FS_SYS_TEMPLATES ?? '';
        $template_file = $template_dir . DS . $template_key . '.hilt.php';
        print_rr($template_file,'template_file');
        if (file_exists($template_file)) {
            $content = file_get_contents($template_file);
            if (preg_match("/'type' => 'hilt-([^']+)'/", $content, $matches)) {
                $template_type = $matches[1];
            } elseif (preg_match("/'type' => '([^']+)'/", $content, $matches)) {
                $template_type = $matches[1];
            }
        }
    }
    print_rr($template_type,'get_template_fields $template_type');
    // Return appropriate component based on template type
    switch ($template_type) {
        case 'csv':
            return Edge::render('component-forms-csv-upload', [
                'name' => 'template_data',
                'filetype' => '.csv,.txt',
                'uploadLocation' => 'uploads/csv/',
                'required' => false
            ]);

        case 'custom_html':
            return Edge::render('component-forms-html-editor', [
                'name' => 'template_data[html]',
                'required' => false,
                'rows' => 6
            ]);

        case 'file_upload':
            return Edge::render('component-forms-file-upload', [
                'name' => 'template_file',
                'filetype' => '.html,.php,.txt',
                'uploadLocation' => 'uploads/templates/',
                'required' => false
            ]);

        case 'default':
        default:
            return Edge::render('component-forms-basic-info', [
                'template_type' => 'default'
            ]);
    }
}

function update_route_key($p){
    $input = str_replace([' ','-'],'_',$p['name'] ?? '');
    $input = strtolower(preg_replace('/[^a-zA-Z0-9_]/', '', $input));
    return Edge::render('forms-input',[
        'name'=>"key",
        'id'=>"route_key_input",
        'label'=>"Route Key",
        'placeholder'=>"e.g., dashboard",
        'value' => $input,
        'required'
    ]);

}

/**
 * Check if moving an item would create a circular reference
 *
 * @param string $item_key The item being moved
 * @param string $new_parent_path The proposed new parent path
 * @return bool True if it would create a circular reference
 */
function wouldCreateCircularReference($item_key, $new_parent_path) {
    // If the new parent path contains the item key, it would create a circular reference
    $path_parts = explode('/', $new_parent_path);
    return in_array($item_key, $path_parts);
}

