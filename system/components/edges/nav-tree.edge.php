@props([
    'items' => [],
    'parent_path' => 'root',
    'depth' => 0,
])
@if($parent_path == 'root')
    @php
        $parent_path = '';
    @endphp
@endif

@if($depth == 0)
<div x-data="initNavTree()" x-init="init()">
@endif

@if(in_array(USER_ROLE, ['admin', 'dev']))
<form class="nav-sortable @if($depth > 0) bg-indigo-{{ $depth+6 }}00 shadow-inner shadow-indigo-{{ $depth+7 }}00 @endif"
      hx-post="{{ APP_ROOT }}/api/nav_tree/reorder_navigation"
      hx-trigger="end"
      hx-target="body"
      hx-swap="none"
      data-parent-path="{{ $parent_path }}"
      data-depth="{{ $depth }}">
    <div class="htmx-indicator">Updating navigation...</div>
    <input type="hidden" name="parent_path" value="{{ $parent_path }}">
@else
<ul @if($depth > 0) class="bg-indigo-{{ $depth+6 }}00 shadow-inner shadow-indigo-{{ $depth+7 }}00" @endif>
@endif

    @foreach($items as $key => $item)
        @if( !empty($item['required_roles']) && is_array($item['required_roles']) && count($item['required_roles']) > 0 && !in_array(USER_ROLE, $item['required_roles']) ) @php continue; @endphp @endif
        @if(in_array(USER_ROLE, ['admin', 'dev']))
        <div :class='collapsed ? "mx-0" : "mx-0"'
             class="relative group sortable-item"
             data-route-key="{{ $key }}"
             data-parent-path="{{ $parent_path }}"
             data-item-name="{{ $item['name'] }}">
            <input type='hidden' name='item' value='{{ $key }}' />
        @else
        <li :class='collapsed ? "mx-0" : "mx-0"'
            class="relative group"
            data-route-key="{{ $key }}"
            data-parent-path="{{ $parent_path }}"
            data-item-name="{{ $item['name'] }}">
        @endif
            @php
                $item_route = trim($parent_path . '/' . $key, '/');
                $url_path = str_replace('//', '/', APP_ROOT . '/' . $parent_path . '/' . $key);
            @endphp
            <a href='{{ $url_path }}'
               hx-get='{{ $url_path }}'
               hx-replace-url='{{ $url_path }}'
               hx-target='#content_wrapper'
               hx-swap='innerHTML'
               hx-on::before-request="console.log('Loading:', '{{ $item_route }}')"
               hx-on::after-request="console.log('Loaded:', '{{ $item_route }}', event.detail.xhr.status); updateNavRoute('{{ $item_route }}')"
               hx-on::response-error="console.error('Error loading:', '{{ $item_route }}', event.detail.xhr.status, event.detail.xhr.responseText)"
               @click="updateRoute('{{ $item_route }}')"
               class='flex gap-x-3 p-2 text-sm font-semibold leading-6 group hover:text-white inset-shadow-sm'
               :class="currentRoute === '{{ $item_route }}' || (currentRoute.startsWith('{{ $item_route }}/') && {{ isset($item['sub_folder']) ? 'true' : 'false' }}) ? 'border-y-2 border-y-green-600 text-white bg-green-700' : 'text-indigo-200 border-y-2 border-y-indigo-{{ $depth+6 }}00 hover:bg-indigo-{{ $depth + 6 + 1 }}00'"
            >
                @if(in_array(USER_ROLE, ['admin', 'dev']))
                    <span class='shrink-0 drag-handle cursor-move' title="Drag to reorder">{{ ICONS[$item['icon']] }}</span>
                @else
                    <span class='shrink-0'>{{ ICONS[$item['icon']] }}</span>
                @endif
                <span class='transition-opacity duration-300 whitespace-nowrap' :class='collapsed ? "opacity-0" : "opacity-100"'>
                    {{ $item['name'] }}
                </span>
            </a>
            @if(in_array(USER_ROLE, ['admin', 'dev']) && !isset($item['sub_folder']) && $item['can_delete'])
                <button
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-red-400 hover:text-red-600 p-1 rounded"
                    hx-delete="{{ APP_ROOT }}/api/nav_tree/delete_nav_entry"
                    hx-vals='{"parent_path": "{{ $parent_path }}", "key": "{{ $key }}"}'
                    hx-confirm="Are you sure you want to delete '{{ $item['name'] }}'? This action cannot be undone."
                    hx-target="closest li"
                    title="Delete {{ $item['name'] }}"
                    @click.stop
                >
                    <span class="shrink-0 w-4 h-4 block">
                        @icon('trash')
                    </span>
                </button>
            @endif
            @if(isset($item['sub_folder']))
                 <x-nav-tree :items="$item['sub_folder']" :parent_path="trim($parent_path . '/' . $key, '/')" :depth="$depth + 1"></x-nav-tree>
            @endif
        @if(in_array(USER_ROLE, ['admin', 'dev']))
        </div>
        @else
        </li>
        @endif
    @endforeach

    @if(in_array(USER_ROLE, ['admin', 'dev']))
        <div :class='collapsed ? "mx-1" : "mx-2"' class="mt-2">
            <button
                hx-post="{{ APP_ROOT }}/api/nav_tree/add_nav_entry"
                hx-vals='{"parent_path": "{{ $parent_path }}"}'
                hx-target="#modal_body"
                @click='showModal = true'
                class="flex gap-x-3 p-2 text-sm font-semibold leading-6 text-green-400 rounded-md group hover:text-white hover:bg-green-700 w-full"
            >
                <span class="shrink-0">{{ ICONS['plus'] }}</span>
                <span class="transition-opacity duration-300 whitespace-nowrap" :class='collapsed ? "opacity-0" : "opacity-100"'>
                    Add Entry
                </span>
            </button>
        </div>
</form>
@else
</ul>
@endif

@if($depth == 0)
</div>
@endif