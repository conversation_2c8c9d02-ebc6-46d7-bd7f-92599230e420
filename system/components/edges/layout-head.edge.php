<!doctype html>
<html class="h-full bg-white">
<head><title>Autobooks</title>
<link rel="stylesheet" href="https://rsms.me/inter/inter.css">
<!--<link rel="stylesheet" href="css/style.css">-->
<link rel="shortcut icon" href="{{ APP_ROOT }}/system/img/favicon.ico" />
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>

<!-- Jodit Editor -->
<link rel="stylesheet" href="https://unpkg.com/jodit@3/build/jodit.min.css"/>
<script src="https://unpkg.com/jodit@3/build/jodit.min.js"></script>

<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
<script defer src="https://unpkg.com/htmx.org@2.0.3"></script>
<script defer src="https://unpkg.com/htmx-ext-class-tools@2.0.1/class-tools.js"></script>
<script defer src="<?= APP_ROOT ?>/resources/components/js/notification-handler.js"></script>
<script defer src="<?= APP_ROOT ?>/resources/js/htmx-sse.js"></script>

    <script>
        // Navigation tree initialization function - simplified for sortable only
        function initNavTree() {
            return {
                currentRoute: localStorage.getItem('currentNavRoute') || window.location.pathname.replace('<?= APP_ROOT ?>', '').replace(/^\/+|\/+$/g, ''),
                init() {
                    // Set initial route based on current URL
                    if (!localStorage.getItem('currentNavRoute')) {
                        localStorage.setItem('currentNavRoute', this.currentRoute);
                    }
                },
                updateRoute(route) {
                    this.currentRoute = route;
                    localStorage.setItem('currentNavRoute', route);
                    // Dispatch event to sync other nav instances
                    window.dispatchEvent(new CustomEvent('nav-route-changed', { detail: { route: route } }));
                }
            };
        }

        // Global function to update route from HTMX
        window.updateNavRoute = function(route) {
            const navTree = document.querySelector('[x-data*="initNavTree"]');
            if (navTree && navTree.__x && navTree.__x.$data) {
                navTree.__x.$data.updateRoute(route);
            }
        };

        // Listen for route changes from other nav instances
        window.addEventListener('nav-route-changed', (event) => {
            // Update all nav tree instances
            document.querySelectorAll('[x-data*="initNavTree"]').forEach(el => {
                if (el._x_dataStack && el._x_dataStack[0]) {
                    el._x_dataStack[0].currentRoute = event.detail.route;
                }
            });
        });

        // HTMX Sortable Integration for Navigation Tree
        htmx.onLoad(function(content) {
            // Navigation tree sorting
            var navSortables = content.querySelectorAll(".nav-sortable");
            for (var i = 0; i < navSortables.length; i++) {
                var sortable = navSortables[i];
                var sortableInstance = new Sortable(sortable, {
                    group: {
                        name: 'nav-tree',
                        pull: true,
                        put: true
                    },
                    animation: 150,
                    handle: '.drag-handle',
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    fallbackOnBody: true,
                    swapThreshold: 0.65,
                    // Make the `.htmx-indicator` unsortable
                    filter: ".htmx-indicator",
                    onMove: function (evt) {
                        return evt.related.className.indexOf('htmx-indicator') === -1 &&
                               evt.related.classList.contains('sortable-item');
                    },
                    onStart: function (evt) {
                        // Add visual feedback
                        document.body.classList.add('sorting-active');
                        evt.item.classList.add('dragging');
                    },
                    // Disable sorting on the `end` event
                    onEnd: function (evt) {
                        // Remove visual feedback
                        document.body.classList.remove('sorting-active');
                        evt.item.classList.remove('dragging');
                        this.option("disabled", true);
                    }
                });

                // Re-enable sorting on the `htmx:afterSwap` event
                sortable.addEventListener("htmx:afterSwap", function() {
                    sortableInstance.option("disabled", false);
                });
            }


            // Column sorting
            var columnSortables = content.querySelectorAll(".column-sortable");
            for (var i = 0; i < columnSortables.length; i++) {
                var sortable = columnSortables[i];
                var sortableInstance = new Sortable(sortable, {
                    animation: 150,
                    handle: '.column-drag-handle',
                    filter: '.htmx-indicator',
                    ghostClass: 'sortable-ghost',
                    onMove: function (evt) {
                        return evt.related.className.indexOf('htmx-indicator') === -1;
                    },
                    onEnd: function (evt) {
                        this.option("disabled", true);
                    }
                });

                // Re-enable sorting on the htmx:afterSwap event
                sortable.addEventListener("htmx:afterSwap", function() {
                    sortableInstance.option("disabled", false);
                });
            }

            // Field and action sorting
            var fieldSortables = content.querySelectorAll(".field-container");
            for (var j = 0; j < fieldSortables.length; j++) {
                var fieldContainer = fieldSortables[j];
                var fieldSortableInstance = new Sortable(fieldContainer, {
                    group: 'fieldsAndActions',
                    animation: 150,
                    handle: '.field-drag-handle, .action-drag-handle',
                    filter: '.htmx-indicator',
                    ghostClass: 'sortable-ghost',
                    onMove: function (evt) {
                        return evt.related.className.indexOf('htmx-indicator') === -1;
                    },
                    onEnd: function (evt) {
                        // Handle field/action movement between columns
                        var targetColumnId = evt.to.closest('[data-column-id]').dataset.columnId;
                        var sourceColumnId = evt.from.closest('[data-column-id]').dataset.columnId;

                        if (targetColumnId !== sourceColumnId) {
                            var fieldName = evt.item.dataset.fieldName;
                            var actionId = evt.item.dataset.actionId;

                            if (fieldName) {
                                // Move field between columns
                                htmx.ajax('POST', APP_ROOT + '/api/data_table/column_preferences/move_field', {
                                    values: {
                                        table_name: evt.to.closest('.column-sortable').dataset.tableName,
                                        callback: evt.to.closest('.column-sortable').dataset.callback,
                                        data_source: evt.to.closest('.column-sortable').dataset.dataSource,
                                        field_name: fieldName,
                                        source_column_id: sourceColumnId,
                                        target_column_id: targetColumnId
                                    },
                                    target: '.data_table',
                                    swap: 'outerHTML'
                                });
                            } else if (actionId) {
                                // Move action button between columns
                                htmx.ajax('POST', APP_ROOT + '/api/data_table/column_preferences/move_action_button', {
                                    values: {
                                        table_name: evt.to.closest('.column-sortable').dataset.tableName,
                                        callback: evt.to.closest('.column-sortable').dataset.callback,
                                        data_source: evt.to.closest('.column-sortable').dataset.dataSource,
                                        action_id: actionId,
                                        source_column_id: sourceColumnId,
                                        target_column_id: targetColumnId
                                    },
                                    target: '.data_table',
                                    swap: 'outerHTML'
                                });
                            }
                        }

                        this.option("disabled", true);
                    }
                });

                // Re-enable sorting on the htmx:afterSwap event
                fieldContainer.addEventListener("htmx:afterSwap", function() {
                    fieldSortableInstance.option("disabled", false);
                });
            }
        });

    </script>


<style>
    .htmx-settling-in {
        background-color: yellow;
        transition: background-color 1s ease-in-out;
    }
    .htmx-settling-out {
        background-color: white;
        transition: background-color 1s ease-in-out;
    }

    /* Sortable styles */
    .sortable-ghost {
        opacity: 0.4;
        background: rgba(59, 130, 246, 0.1) !important;
    }

    .sortable-chosen {
        background: rgba(59, 130, 246, 0.2) !important;
    }

    .sortable-drag {
        transform: rotate(5deg);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .drop-target {
        background: rgba(34, 197, 94, 0.2) !important;
        border: 2px dashed #22c55e !important;
    }

    .sorting-active .drag-handle {
        cursor: grabbing !important;
    }

    .drag-handle:hover {
        opacity: 0.8;
        transform: scale(1.1);
        transition: all 0.2s ease;
    }

    .dragging {
        opacity: 0.6;
        transform: rotate(2deg);
    }

    /* Ensure proper z-index for dragged items */
    .sortable-drag {
        z-index: 9999 !important;
    }

    /* Style for nested lists to maintain depth appearance */
    [data-depth="1"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.3);
    }

    [data-depth="2"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.5);
    }

    [data-depth="3"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.7);
    }
</style>
<style>
    html {
        font-family: InterVariable, sans-serif !important;
    }
</style>
    <!-- Include Jodit CSS and JS -->
    <link rel="stylesheet" href="https://unpkg.com/jodit@3/build/jodit.min.css"/>
    <script src="https://unpkg.com/jodit@3/build/jodit.min.js"></script>
</head>
