<!doctype html>
<html class="h-full bg-white">
<head><title>Autobooks</title>
<link rel="stylesheet" href="https://rsms.me/inter/inter.css">
<!--<link rel="stylesheet" href="css/style.css">-->
<link rel="shortcut icon" href="{{ APP_ROOT }}/system/img/favicon.ico" />
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>

<!-- Jodit Editor -->
<link rel="stylesheet" href="https://unpkg.com/jodit@3/build/jodit.min.css"/>
<script src="https://unpkg.com/jodit@3/build/jodit.min.js"></script>

<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
<script defer src="https://unpkg.com/htmx.org@2.0.3"></script>
<script defer src="https://unpkg.com/htmx-ext-class-tools@2.0.1/class-tools.js"></script>
<script defer src="resources/components/js/notification-handler.js"></script>
<script defer src="<?= APP_ROOT ?>/resources/js/htmx-sse.js"></script>

    <script>
        // Define APP_ROOT for JavaScript files
        var APP_ROOT = '<?= APP_ROOT ?>';

        // Navigation tree initialization function
        function initNavTree() {
            return {
                currentRoute: localStorage.getItem('currentNavRoute') || window.location.pathname.replace(APP_ROOT, '').replace(/^\/+|\/+$/g, ''),
                sortableInstances: [],
                init() {
                    // Set initial route based on current URL
                    if (!localStorage.getItem('currentNavRoute')) {
                        localStorage.setItem('currentNavRoute', this.currentRoute);
                    }

                    // Initialize sortable functionality after a short delay
                    setTimeout(() => this.initSortable(), 100);

                    // Listen for HTMX after-request events
                    document.body.addEventListener('htmx:afterOnLoad', (event) => {
                        // Don't update if this is a background request (not navigation)
                        if (event.detail.target.id !== 'content_wrapper') return;

                        // Extract the path from the URL
                        const url = new URL(event.detail.xhr.responseURL);
                        const path = url.pathname.replace(APP_ROOT, '');
                        const cleanPath = path.replace(/^\/+|\/+$/g, '');

                        // Update the current route
                        this.currentRoute = cleanPath;
                        localStorage.setItem('currentNavRoute', cleanPath);

                        // Dispatch a custom event to synchronize all nav-tree instances
                        window.dispatchEvent(new CustomEvent('nav-route-changed', { detail: { route: cleanPath } }));
                    });

                    // Listen for route change events from other nav-tree instances
                    window.addEventListener('nav-route-changed', (event) => {
                        this.currentRoute = event.detail.route;
                    });

                    // Reinitialize sortable after HTMX updates
                    document.body.addEventListener('htmx:afterSettle', () => {
                        this.destroySortable();
                        setTimeout(() => this.initSortable(), 100);
                    });
                },
                destroySortable() {
                    // Destroy existing sortable instances
                    this.sortableInstances.forEach(instance => {
                        if (instance && instance.destroy) {
                            instance.destroy();
                        }
                    });
                    this.sortableInstances = [];
                },
                initSortable() {
                    // Only initialize if user has admin/dev role
                    if (!document.querySelector('.drag-handle')) return;

                    // Find all sortable lists
                    const sortableLists = document.querySelectorAll('[data-sortable="true"]');

                    sortableLists.forEach(list => {
                        const parentPath = list.dataset.parentPath || 'root';
                        const depth = parseInt(list.dataset.depth || '0');

                        const sortable = Sortable.create(list, {
                            group: {
                                name: 'nav-tree',
                                pull: true,
                                put: true
                            },
                            animation: 150,
                            handle: '.drag-handle',
                            ghostClass: 'sortable-ghost',
                            chosenClass: 'sortable-chosen',
                            dragClass: 'sortable-drag',
                            fallbackOnBody: true,
                            swapThreshold: 0.65,
                            onStart: (evt) => {
                                // Add visual feedback
                                document.body.classList.add('sorting-active');
                                evt.item.classList.add('dragging');
                            },
                            onEnd: (evt) => {
                                // Remove visual feedback
                                document.body.classList.remove('sorting-active');
                                evt.item.classList.remove('dragging');

                                // Handle the drop
                                this.handleSortableEnd(evt, parentPath);
                            },
                            onMove: (evt) => {
                                // Prevent dropping on non-sortable items
                                return evt.related.classList.contains('sortable-item');
                            }
                        });

                        this.sortableInstances.push(sortable);
                    });
                },
                handleSortableEnd(evt, originalParentPath) {
                    const item = evt.item;
                    const newIndex = evt.newIndex;
                    const oldIndex = evt.oldIndex;
                    const from = evt.from;
                    const to = evt.to;

                    // Get the actual parent paths from the containers
                    const fromParentPath = from.dataset.parentPath || 'root';
                    const toParentPath = to.dataset.parentPath || 'root';

                    // Check if item was moved to a different list (subfolder creation)
                    if (from !== to) {
                        const itemKey = item.dataset.routeKey;

                        // Move to different parent (subfolder)
                        htmx.ajax('POST', APP_ROOT + '/api/nav_tree/move_to_subfolder', {
                            values: {
                                item_key: itemKey,
                                current_parent: fromParentPath,
                                target_parent: toParentPath
                            },
                            target: 'body',
                            swap: 'none'
                        }).then(() => {
                            // Refresh the navigation tree after successful move
                            setTimeout(() => window.location.reload(), 500);
                        }).catch(() => {
                            // Revert the move on error
                            from.insertBefore(item, from.children[oldIndex]);
                        });
                    } else if (oldIndex !== newIndex) {
                        // Reorder within same parent
                        const items = Array.from(to.querySelectorAll('.sortable-item')).map((el, index) => ({
                            route_key: el.dataset.routeKey,
                            sort_order: index + 1
                        }));

                        htmx.ajax('POST', APP_ROOT + '/api/nav_tree/reorder_navigation', {
                            values: {
                                items: JSON.stringify(items),
                                parent_path: toParentPath
                            },
                            target: 'body',
                            swap: 'none'
                        }).catch(() => {
                            // Revert the move on error
                            if (oldIndex < newIndex) {
                                to.insertBefore(item, to.children[oldIndex]);
                            } else {
                                to.insertBefore(item, to.children[oldIndex + 1]);
                            }
                        });
                    }
                }
            };
        }

        // HTMX Sortable Integration for Column Manager
        htmx.onLoad(function(content) {
            // Column sorting
            var columnSortables = content.querySelectorAll(".column-sortable");
            for (var i = 0; i < columnSortables.length; i++) {
                var sortable = columnSortables[i];
                var sortableInstance = new Sortable(sortable, {
                    animation: 150,
                    handle: '.column-drag-handle',
                    filter: '.htmx-indicator',
                    ghostClass: 'sortable-ghost',
                    onMove: function (evt) {
                        return evt.related.className.indexOf('htmx-indicator') === -1;
                    },
                    onEnd: function (evt) {
                        this.option("disabled", true);
                    }
                });

                // Re-enable sorting on the htmx:afterSwap event
                sortable.addEventListener("htmx:afterSwap", function() {
                    sortableInstance.option("disabled", false);
                });
            }

            // Field and action sorting
            var fieldSortables = content.querySelectorAll(".field-container");
            for (var j = 0; j < fieldSortables.length; j++) {
                var fieldContainer = fieldSortables[j];
                var fieldSortableInstance = new Sortable(fieldContainer, {
                    group: 'fieldsAndActions',
                    animation: 150,
                    handle: '.field-drag-handle, .action-drag-handle',
                    filter: '.htmx-indicator',
                    ghostClass: 'sortable-ghost',
                    onMove: function (evt) {
                        return evt.related.className.indexOf('htmx-indicator') === -1;
                    },
                    onEnd: function (evt) {
                        // Handle field/action movement between columns
                        var targetColumnId = evt.to.closest('[data-column-id]').dataset.columnId;
                        var sourceColumnId = evt.from.closest('[data-column-id]').dataset.columnId;

                        if (targetColumnId !== sourceColumnId) {
                            var fieldName = evt.item.dataset.fieldName;
                            var actionId = evt.item.dataset.actionId;

                            if (fieldName) {
                                // Move field between columns
                                htmx.ajax('POST', APP_ROOT + '/api/data_table/column_preferences/move_field', {
                                    values: {
                                        table_name: evt.to.closest('.column-sortable').dataset.tableName,
                                        callback: evt.to.closest('.column-sortable').dataset.callback,
                                        data_source: evt.to.closest('.column-sortable').dataset.dataSource,
                                        field_name: fieldName,
                                        source_column_id: sourceColumnId,
                                        target_column_id: targetColumnId
                                    },
                                    target: '.data_table',
                                    swap: 'outerHTML'
                                });
                            } else if (actionId) {
                                // Move action button between columns
                                htmx.ajax('POST', APP_ROOT + '/api/data_table/column_preferences/move_action_button', {
                                    values: {
                                        table_name: evt.to.closest('.column-sortable').dataset.tableName,
                                        callback: evt.to.closest('.column-sortable').dataset.callback,
                                        data_source: evt.to.closest('.column-sortable').dataset.dataSource,
                                        action_id: actionId,
                                        source_column_id: sourceColumnId,
                                        target_column_id: targetColumnId
                                    },
                                    target: '.data_table',
                                    swap: 'outerHTML'
                                });
                            }
                        }

                        this.option("disabled", true);
                    }
                });

                // Re-enable sorting on the htmx:afterSwap event
                fieldContainer.addEventListener("htmx:afterSwap", function() {
                    fieldSortableInstance.option("disabled", false);
                });
            }
        });

    </script>


<style>
    .htmx-settling-in {
        background-color: yellow;
        transition: background-color 1s ease-in-out;
    }
    .htmx-settling-out {
        background-color: white;
        transition: background-color 1s ease-in-out;
    }

    /* Sortable styles */
    .sortable-ghost {
        opacity: 0.4;
        background: rgba(59, 130, 246, 0.1) !important;
    }

    .sortable-chosen {
        background: rgba(59, 130, 246, 0.2) !important;
    }

    .sortable-drag {
        transform: rotate(5deg);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .drop-target {
        background: rgba(34, 197, 94, 0.2) !important;
        border: 2px dashed #22c55e !important;
    }

    .sorting-active .drag-handle {
        cursor: grabbing !important;
    }

    .drag-handle:hover {
        opacity: 0.8;
        transform: scale(1.1);
        transition: all 0.2s ease;
    }

    .dragging {
        opacity: 0.6;
        transform: rotate(2deg);
    }

    /* Ensure proper z-index for dragged items */
    .sortable-drag {
        z-index: 9999 !important;
    }

    /* Style for nested lists to maintain depth appearance */
    [data-depth="1"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.3);
    }

    [data-depth="2"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.5);
    }

    [data-depth="3"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.7);
    }
</style>
<style>
    html {
        font-family: InterVariable, sans-serif !important;
    }
</style>
    <!-- Include Jodit CSS and JS -->
    <link rel="stylesheet" href="https://unpkg.com/jodit@3/build/jodit.min.css"/>
    <script src="https://unpkg.com/jodit@3/build/jodit.min.js"></script>
</head>
